use std::sync::Arc;

use anyhow::Result;
use tracing::{error, info};

use crate::{
    common::global_ctx::ArcGlobalCtx,
    gateway::proxy_forward_manager::ProxyForwardManager,
    peers::peer_manager::PeerManager,
};

/// 代理转发集成器，负责将代理转发功能集成到EasyTier主程序中
pub struct ProxyForwardIntegration {
    global_ctx: ArcGlobalCtx,
    peer_manager: Arc<PeerManager>,
    proxy_manager: Option<Arc<ProxyForwardManager>>,
}

impl ProxyForwardIntegration {
    pub fn new(global_ctx: ArcGlobalCtx, peer_manager: Arc<PeerManager>) -> Self {
        Self {
            global_ctx,
            peer_manager,
            proxy_manager: None,
        }
    }

    /// 启动代理转发功能
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting proxy forward integration");

        // 检查是否配置了代理转发
        let has_proxy_config = self.global_ctx.config.get_proxy_forwards().is_some();

        if !has_proxy_config {
            info!("No proxy forward configuration found, skipping proxy forward setup");
            return Ok(());
        }

        // 创建代理转发管理器
        let mut proxy_manager = ProxyForwardManager::new(
            self.global_ctx.clone(),
            self.peer_manager.clone(),
        );

        // 启动代理转发服务
        match proxy_manager.start().await {
            Ok(_) => {
                info!("Proxy forward manager started successfully");
                self.proxy_manager = Some(Arc::new(proxy_manager));
            }
            Err(e) => {
                error!("Failed to start proxy forward manager: {}", e);
                return Err(e);
            }
        }

        Ok(())
    }

    /// 停止代理转发功能
    pub async fn stop(&mut self) -> Result<()> {
        if let Some(_proxy_manager) = self.proxy_manager.take() {
            info!("Stopping proxy forward integration");
            // 在实际实现中，这里会调用proxy_manager的stop方法
            // proxy_manager.stop().await?;
            info!("Proxy forward integration stopped");
        }
        Ok(())
    }

    /// 获取代理转发管理器
    pub fn get_proxy_manager(&self) -> Option<Arc<ProxyForwardManager>> {
        self.proxy_manager.clone()
    }

    /// 动态更新代理转发配置
    pub async fn update_config(&mut self) -> Result<()> {
        let new_config = self.global_ctx.config.get_proxy_forwards();

        match &mut self.proxy_manager {
            Some(_proxy_manager) => {
                // 创建一个新的管理器实例来更新配置
                let mut new_manager = ProxyForwardManager::new(
                    self.global_ctx.clone(),
                    self.peer_manager.clone(),
                );

                match new_manager.update_config(new_config).await {
                    Ok(_) => {
                        info!("Proxy forward configuration updated successfully");
                        // 在实际实现中，这里需要更复杂的配置热更新逻辑
                    }
                    Err(e) => {
                        error!("Failed to update proxy forward configuration: {}", e);
                        return Err(e);
                    }
                }
            }
            None => {
                if new_config.is_some() {
                    // 如果之前没有配置，现在有了配置，则启动服务
                    self.start().await?;
                }
            }
        }

        Ok(())
    }

    /// 获取代理转发状态信息
    pub fn get_status(&self) -> ProxyForwardStatus {
        match &self.proxy_manager {
            Some(proxy_manager) => {
                let has_client = proxy_manager.get_client().is_some();
                let has_server = true; // 服务器总是存在

                ProxyForwardStatus {
                    enabled: true,
                    client_active: has_client,
                    server_active: has_server,
                    active_sessions: 0, // 在实际实现中从管理器获取
                }
            }
            None => ProxyForwardStatus {
                enabled: false,
                client_active: false,
                server_active: false,
                active_sessions: 0,
            }
        }
    }

    /// 列出当前的代理转发配置
    pub fn list_proxy_configs(&self) -> Vec<ProxyConfigInfo> {
        let mut configs = Vec::new();

        if let Some(proxy_config) = self.global_ctx.config.get_proxy_forwards() {
            for (env_name, env_config) in proxy_config.environments {
                for port_mapping in env_config.port {
                    if let Some((local_port, remote_port)) = parse_port_mapping(&port_mapping) {
                        configs.push(ProxyConfigInfo {
                            environment: env_name.clone(),
                            server: env_config.server.clone(),
                            local_port,
                            remote_port,
                            status: ProxyConfigStatus::Configured,
                        });
                    }
                }
            }
        }

        configs
    }

    /// 验证代理转发配置
    pub fn validate_config(&self) -> Vec<ConfigValidationError> {
        let mut errors = Vec::new();

        if let Some(proxy_config) = self.global_ctx.config.get_proxy_forwards() {
            for (env_name, env_config) in &proxy_config.environments {
                // 验证服务器地址
                if env_config.server.is_empty() {
                    errors.push(ConfigValidationError {
                        environment: env_name.clone(),
                        error_type: ValidationErrorType::EmptyServer,
                        message: "Server address cannot be empty".to_string(),
                    });
                }

                // 验证端口映射
                for port_mapping in &env_config.port {
                    if parse_port_mapping(port_mapping).is_none() {
                        errors.push(ConfigValidationError {
                            environment: env_name.clone(),
                            error_type: ValidationErrorType::InvalidPortMapping,
                            message: format!("Invalid port mapping format: {}", port_mapping),
                        });
                    }
                }

                // 检查端口冲突
                let mut used_ports = std::collections::HashSet::new();
                for port_mapping in &env_config.port {
                    if let Some((local_port, _)) = parse_port_mapping(port_mapping) {
                        if !used_ports.insert(local_port) {
                            errors.push(ConfigValidationError {
                                environment: env_name.clone(),
                                error_type: ValidationErrorType::PortConflict,
                                message: format!("Duplicate local port: {}", local_port),
                            });
                        }
                    }
                }
            }
        }

        errors
    }
}

/// 代理转发状态信息
#[derive(Debug, Clone)]
pub struct ProxyForwardStatus {
    pub enabled: bool,
    pub client_active: bool,
    pub server_active: bool,
    pub active_sessions: usize,
}

/// 代理配置信息
#[derive(Debug, Clone)]
pub struct ProxyConfigInfo {
    pub environment: String,
    pub server: String,
    pub local_port: u16,
    pub remote_port: u16,
    pub status: ProxyConfigStatus,
}

/// 代理配置状态
#[derive(Debug, Clone)]
pub enum ProxyConfigStatus {
    Configured,
    Active,
    Error(String),
}

/// 配置验证错误
#[derive(Debug, Clone)]
pub struct ConfigValidationError {
    pub environment: String,
    pub error_type: ValidationErrorType,
    pub message: String,
}

/// 验证错误类型
#[derive(Debug, Clone)]
pub enum ValidationErrorType {
    EmptyServer,
    InvalidPortMapping,
    PortConflict,
    InvalidPort,
}

/// 解析端口映射字符串
/// 格式: "本地端口-远程端口"
/// 返回: Some((本地端口, 远程端口)) 或 None
fn parse_port_mapping(mapping: &str) -> Option<(u16, u16)> {
    let parts: Vec<&str> = mapping.split('-').collect();
    if parts.len() != 2 {
        return None;
    }

    let local_port = parts[0].parse::<u16>().ok()?;
    let remote_port = parts[1].parse::<u16>().ok()?;

    Some((local_port, remote_port))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_port_mapping() {
        assert_eq!(parse_port_mapping("8080-80"), Some((8080, 80)));
        assert_eq!(parse_port_mapping("443-443"), Some((443, 443)));
        assert_eq!(parse_port_mapping("22-22"), Some((22, 22)));

        assert_eq!(parse_port_mapping("8080"), None);
        assert_eq!(parse_port_mapping("8080-"), None);
        assert_eq!(parse_port_mapping("-80"), None);
        assert_eq!(parse_port_mapping("abc-80"), None);
        assert_eq!(parse_port_mapping("8080-xyz"), None);
    }

    #[test]
    fn test_proxy_forward_status() {
        let status = ProxyForwardStatus {
            enabled: true,
            client_active: true,
            server_active: true,
            active_sessions: 5,
        };

        assert!(status.enabled);
        assert!(status.client_active);
        assert!(status.server_active);
        assert_eq!(status.active_sessions, 5);
    }
}
