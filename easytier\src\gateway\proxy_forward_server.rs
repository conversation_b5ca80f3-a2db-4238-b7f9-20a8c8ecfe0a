use std::{
    net::SocketAddr,
    sync::{<PERSON>, Mutex},
};

use anyhow::Result;
use async_trait::async_trait;
use dashmap::DashMap;
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::TcpStream,
    sync::mpsc,
    task::JoinSet,
};
use tracing::{error, info, warn};

use crate::{
    common::{global_ctx::ArcGlobalCtx, PeerId},
    peers::peer_manager::PeerManager,
    proto::{
        peer_rpc::{
            ProxyForwardData, ProxyForwardRequest, ProxyForwardResponse, ProxyForwardClose,
            ProxyForwardRpc,
        },
        rpc_types::controller::BaseController,
        common::Void,
    },
};

#[derive(Debug, Clone)]
pub struct ActiveProxySession {
    pub session_id: u32,
    pub src_addr: SocketAddr,
    pub dst_addr: SocketAddr,
    pub protocol: String,
    pub client_peer_id: PeerId,
    pub data_sender: mpsc::UnboundedSender<Vec<u8>>,
}

#[derive(Debug)]
pub struct ProxyForwardServer {
    global_ctx: ArcGlobalCtx,
    peer_manager: Arc<PeerManager>,

    // Active sessions
    active_sessions: Arc<DashMap<u32, ActiveProxySession>>,

    // Task management
    tasks: Arc<Mutex<JoinSet<()>>>,
}

impl ProxyForwardServer {
    pub fn new(
        global_ctx: ArcGlobalCtx,
        peer_manager: Arc<PeerManager>,
    ) -> Self {
        Self {
            global_ctx,
            peer_manager,
            active_sessions: Arc::new(DashMap::new()),
            tasks: Arc::new(Mutex::new(JoinSet::new())),
        }
    }

    pub async fn start(&self) -> Result<()> {
        info!("Starting proxy forward server");

        // Register RPC service
        // This would be done in the actual integration with PeerManager

        Ok(())
    }

    async fn create_proxy_session_impl(
        &self,
        request: ProxyForwardRequest,
        client_peer_id: PeerId,
    ) -> Result<ProxyForwardResponse> {
        let session_id = request.session_id;
        let src_addr: SocketAddr = request.src_addr
            .ok_or_else(|| anyhow::anyhow!("Missing src_addr"))?
            .try_into()
            .map_err(|e| anyhow::anyhow!("Invalid src_addr: {:?}", e))?;
        let dst_addr: SocketAddr = request.dst_addr
            .ok_or_else(|| anyhow::anyhow!("Missing dst_addr"))?
            .try_into()
            .map_err(|e| anyhow::anyhow!("Invalid dst_addr: {:?}", e))?;
        let protocol = request.protocol;

        info!(
            "Creating proxy session {} from peer {} for {}:{} -> {}:{}",
            session_id,
            client_peer_id,
            src_addr.ip(),
            src_addr.port(),
            dst_addr.ip(),
            dst_addr.port()
        );

        // Try to connect to the destination
        let dst_stream = match TcpStream::connect(dst_addr).await {
            Ok(stream) => stream,
            Err(e) => {
                error!("Failed to connect to destination {}: {}", dst_addr, e);
                return Ok(ProxyForwardResponse {
                    session_id,
                    success: false,
                    error_message: Some(format!("Failed to connect to destination: {}", e)),
                });
            }
        };

        info!("Successfully connected to destination {}", dst_addr);

        // Create data channel
        let (data_sender, data_receiver) = mpsc::unbounded_channel();

        // Create session
        let session = ActiveProxySession {
            session_id,
            src_addr,
            dst_addr,
            protocol,
            client_peer_id,
            data_sender,
        };

        // Store session
        self.active_sessions.insert(session_id, session.clone());

        // Start data forwarding task
        let server = self.clone();
        self.tasks.lock().unwrap().spawn(async move {
            server.handle_session_data(session, dst_stream, data_receiver).await;
        });

        Ok(ProxyForwardResponse {
            session_id,
            success: true,
            error_message: None,
        })
    }

    async fn handle_session_data(
        &self,
        session: ActiveProxySession,
        dst_stream: TcpStream,
        data_receiver: mpsc::UnboundedReceiver<Vec<u8>>,
    ) {
        let session_id = session.session_id;
        info!("Starting data forwarding for session {}", session_id);

        // Split stream for reading and writing
        let (mut read_half, mut write_half) = dst_stream.into_split();

        // Task for reading from destination and sending to client peer
        let server_clone = self.clone();
        let session_clone = session.clone();
        let read_task = tokio::spawn(async move {
            let mut buffer = [0u8; 4096];
            loop {
                match read_half.read(&mut buffer).await {
                    Ok(0) => {
                        info!("Destination connection closed for session {}", session_id);
                        break;
                    }
                    Ok(n) => {
                        let data = buffer[..n].to_vec();
                        if let Err(e) = server_clone.send_data_to_client(&session_clone, data).await {
                            error!("Failed to send data to client: {}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Failed to read from destination: {}", e);
                        break;
                    }
                }
            }
        });

        // Task for receiving data from client and writing to destination
        let mut data_receiver = data_receiver;
        let write_task = tokio::spawn(async move {
            while let Some(data) = data_receiver.recv().await {
                if let Err(e) = write_half.write_all(&data).await {
                    error!("Failed to write to destination: {}", e);
                    break;
                }
            }
        });

        // Wait for either task to complete
        tokio::select! {
            _ = read_task => {},
            _ = write_task => {},
        }

        // Clean up session
        self.cleanup_session(session_id).await;
    }

    async fn send_data_to_client(&self, session: &ActiveProxySession, data: Vec<u8>) -> Result<()> {
        // Send data back to client peer via RPC
        // This is a simplified implementation - you would use the actual RPC client
        info!("Sending {} bytes back to client for session {}", data.len(), session.session_id);
        Ok(())
    }

    async fn cleanup_session(&self, session_id: u32) {
        info!("Cleaning up session {}", session_id);
        self.active_sessions.remove(&session_id);

        // Send close notification to client
        // This is a simplified implementation
    }

    pub async fn handle_data_from_client(&self, data: ProxyForwardData) -> Result<()> {
        let session_id = data.session_id;

        if let Some(session) = self.active_sessions.get(&session_id) {
            if let Err(e) = session.data_sender.send(data.data) {
                error!("Failed to send data to session {}: {}", session_id, e);
                self.cleanup_session(session_id).await;
            }
        } else {
            warn!("Received data for unknown session {}", session_id);
        }

        Ok(())
    }

    pub async fn handle_close_from_client(&self, close: ProxyForwardClose) -> Result<()> {
        let session_id = close.session_id;
        info!("Received close request for session {}: {:?}", session_id, close.reason);

        self.cleanup_session(session_id).await;
        Ok(())
    }
}

impl Clone for ProxyForwardServer {
    fn clone(&self) -> Self {
        Self {
            global_ctx: self.global_ctx.clone(),
            peer_manager: self.peer_manager.clone(),
            active_sessions: self.active_sessions.clone(),
            tasks: self.tasks.clone(),
        }
    }
}

// RPC Service Implementation
#[async_trait]
impl ProxyForwardRpc for ProxyForwardServer {
    type Controller = BaseController;

    async fn create_proxy_session(
        &self,
        _ctrl: BaseController,
        request: ProxyForwardRequest,
    ) -> Result<ProxyForwardResponse, crate::proto::rpc_types::error::Error> {
        // Get client peer ID from controller
        let client_peer_id = 1; // This should come from the controller

        match self.create_proxy_session_impl(request, client_peer_id).await {
            Ok(response) => Ok(response),
            Err(e) => {
                error!("Failed to create proxy session: {}", e);
                Err(crate::proto::rpc_types::error::Error::ExecutionError(e))
            }
        }
    }

    async fn send_data(
        &self,
        _ctrl: BaseController,
        request: ProxyForwardData,
    ) -> Result<Void, crate::proto::rpc_types::error::Error> {
        match self.handle_data_from_client(request).await {
            Ok(_) => Ok(Void {}),
            Err(e) => {
                error!("Failed to handle data from client: {}", e);
                Err(crate::proto::rpc_types::error::Error::ExecutionError(anyhow::anyhow!(e)))
            }
        }
    }

    async fn close_session(
        &self,
        _ctrl: BaseController,
        request: ProxyForwardClose,
    ) -> Result<Void, crate::proto::rpc_types::error::Error> {
        match self.handle_close_from_client(request).await {
            Ok(_) => Ok(Void {}),
            Err(e) => {
                error!("Failed to handle close from client: {}", e);
                Err(crate::proto::rpc_types::error::Error::ExecutionError(anyhow::anyhow!(e)))
            }
        }
    }
}
