use std::{
    net::SocketAddr,
    pin::Pin,
    sync::Arc,
    task::{Context, Poll},
};

use crate::common::error::Error;
use anyhow;
use async_trait::async_trait;
use pnet::packet::ipv4::Ipv4Packet;
use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};

use crate::{
    common::global_ctx::{ArcGlobalCtx, GlobalCtx},
    gateway::{tcp_proxy::NatDstConnector, CidrSet},
    proto::{
        cli::TcpProxyEntryTransportType,
        peer_rpc::ProxyForwardRequest,
    },
    tunnel::packet_def::PeerManagerHeader,
};

use super::proxy_forward_manager::ProxyForwardManager;

#[derive(Debug, Clone)]
pub struct NatDstProxyConnector {
    proxy_manager: Arc<ProxyForwardManager>,
    global_ctx: ArcGlobalCtx,
}

impl NatDstProxyConnector {
    pub fn new(proxy_manager: Arc<ProxyForwardManager>, global_ctx: ArcGlobalCtx) -> Self {
        Self {
            proxy_manager,
            global_ctx,
        }
    }
}

// A wrapper stream that represents a proxy connection
pub struct ProxyStream {
    session_id: u32,
    proxy_manager: Arc<ProxyForwardManager>,
    buffer: Vec<u8>,
    closed: bool,
}

impl ProxyStream {
    pub fn new(session_id: u32, proxy_manager: Arc<ProxyForwardManager>) -> Self {
        Self {
            session_id,
            proxy_manager,
            buffer: Vec::new(),
            closed: false,
        }
    }
}

impl AsyncRead for ProxyStream {
    fn poll_read(
        mut self: Pin<&mut Self>,
        _cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<std::io::Result<()>> {
        if self.closed {
            return Poll::Ready(Ok(()));
        }

        // In a real implementation, this would read from the proxy data channel
        // For now, we'll just return pending to avoid blocking
        if self.buffer.is_empty() {
            Poll::Pending
        } else {
            let to_read = std::cmp::min(buf.remaining(), self.buffer.len());
            let data = self.buffer.drain(..to_read).collect::<Vec<u8>>();
            buf.put_slice(&data);
            Poll::Ready(Ok(()))
        }
    }
}

impl AsyncWrite for ProxyStream {
    fn poll_write(
        self: Pin<&mut Self>,
        _cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<Result<usize, std::io::Error>> {
        if self.closed {
            return Poll::Ready(Err(std::io::Error::new(
                std::io::ErrorKind::BrokenPipe,
                "Stream is closed",
            )));
        }

        // In a real implementation, this would send data through the proxy
        // For now, we'll just pretend we wrote all the data
        Poll::Ready(Ok(buf.len()))
    }

    fn poll_flush(self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Result<(), std::io::Error>> {
        Poll::Ready(Ok(()))
    }

    fn poll_shutdown(
        mut self: Pin<&mut Self>,
        _cx: &mut Context<'_>,
    ) -> Poll<Result<(), std::io::Error>> {
        self.closed = true;
        Poll::Ready(Ok(()))
    }
}

#[async_trait]
impl NatDstConnector for NatDstProxyConnector {
    type DstStream = ProxyStream;

    async fn connect(&self, src: SocketAddr, dst: SocketAddr) -> Result<Self::DstStream, Error> {
        // Check if we have a proxy client available
        let Some(_client) = self.proxy_manager.get_client() else {
            return Err(Error::AnyhowError(anyhow::anyhow!("No proxy client available")));
        };

        // Generate a session ID
        let session_id = rand::random::<u32>();

        // Create a proxy request
        let _request = ProxyForwardRequest {
            session_id,
            src_addr: Some(src.into()),
            dst_addr: Some(dst.into()),
            protocol: "tcp".to_string(),
        };

        // In a real implementation, you would:
        // 1. Send the proxy request to the target peer
        // 2. Wait for the response
        // 3. Set up data channels for bidirectional communication
        // 4. Return a stream that handles the proxy communication

        // For now, we'll create a mock stream
        Ok(ProxyStream::new(session_id, self.proxy_manager.clone()))
    }

    fn check_packet_from_peer_fast(&self, cidr_set: &CidrSet, global_ctx: &GlobalCtx) -> bool {
        // Check if this packet should be handled by the proxy
        // This is a fast check that doesn't parse the packet
        !cidr_set.is_empty() || global_ctx.no_tun()
    }

    fn check_packet_from_peer(
        &self,
        cidr_set: &CidrSet,
        global_ctx: &GlobalCtx,
        _hdr: &PeerManagerHeader,
        ipv4: &Ipv4Packet,
    ) -> bool {
        // Check if this specific packet should be handled by the proxy
        let dst_ip = ipv4.get_destination();

        if global_ctx.no_tun() {
            // In no-tun mode, check if the destination is our local IP
            if let Some(local_ip) = global_ctx.get_ipv4() {
                return dst_ip == local_ip.address();
            }
        }

        // Check if the destination is in our proxy CIDR ranges
        cidr_set.contains_v4(dst_ip)
    }

    fn transport_type(&self) -> TcpProxyEntryTransportType {
        TcpProxyEntryTransportType::Tcp // Use Tcp as placeholder for now
    }
}

// Extension to the TcpProxyEntryTransportType enum
// This would need to be added to the proto definition
// For now, we'll use the existing Tcp type as a placeholder
