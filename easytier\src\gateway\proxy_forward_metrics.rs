use std::{
    collections::HashMap,
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc, RwLock,
    },
    time::{Duration, Instant},
};

use serde::{Deserialize, Serialize};
use tokio::time::interval;
use tracing::{debug, info};

/// 代理转发性能指标收集器
#[derive(Debug)]
pub struct ProxyForwardMetrics {
    // 全局统计
    total_connections: AtomicU64,
    active_connections: AtomicU64,
    total_bytes_sent: AtomicU64,
    total_bytes_received: AtomicU64,

    // 按环境分组的统计
    environment_stats: Arc<RwLock<HashMap<String, EnvironmentStats>>>,

    // 按会话分组的统计
    session_stats: Arc<RwLock<HashMap<u32, SessionStats>>>,

    // 错误统计
    connection_errors: AtomicU64,
    timeout_errors: AtomicU64,

    // 启动时间
    start_time: Instant,
}

/// 环境级别的统计信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EnvironmentStats {
    pub environment_name: String,
    pub server_address: String,
    pub total_connections: u64,
    pub active_connections: u64,
    pub total_bytes_sent: u64,
    pub total_bytes_received: u64,
    pub connection_errors: u64,
    #[serde(skip)]
    pub last_connection_time: Option<Instant>,
    pub port_mappings: Vec<PortMappingStats>,
}

/// 端口映射统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortMappingStats {
    pub local_port: u16,
    pub remote_port: u16,
    pub connections: u64,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    #[serde(skip)]
    pub last_activity: Option<Instant>,
}

/// 会话级别的统计信息
#[derive(Debug)]
pub struct SessionStats {
    pub session_id: u32,
    pub environment: String,
    pub local_port: u16,
    pub remote_port: u16,
    pub start_time: Instant,
    pub bytes_sent: AtomicU64,
    pub bytes_received: AtomicU64,
    pub last_activity: Arc<RwLock<Instant>>,
}

/// 性能指标快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsSnapshot {
    pub uptime_seconds: u64,
    pub total_connections: u64,
    pub active_connections: u64,
    pub total_bytes_sent: u64,
    pub total_bytes_received: u64,
    pub connection_errors: u64,
    pub timeout_errors: u64,
    pub environments: Vec<EnvironmentStats>,
    pub active_sessions: u64,
    pub throughput_bps: f64, // 每秒字节数
}

impl ProxyForwardMetrics {
    pub fn new() -> Self {
        Self {
            total_connections: AtomicU64::new(0),
            active_connections: AtomicU64::new(0),
            total_bytes_sent: AtomicU64::new(0),
            total_bytes_received: AtomicU64::new(0),
            environment_stats: Arc::new(RwLock::new(HashMap::new())),
            session_stats: Arc::new(RwLock::new(HashMap::new())),
            connection_errors: AtomicU64::new(0),
            timeout_errors: AtomicU64::new(0),
            start_time: Instant::now(),
        }
    }

    /// 记录新连接
    pub fn record_connection(&self, environment: &str, local_port: u16, remote_port: u16) {
        self.total_connections.fetch_add(1, Ordering::Relaxed);
        self.active_connections.fetch_add(1, Ordering::Relaxed);

        // 更新环境统计
        let mut env_stats = self.environment_stats.write().unwrap();
        let stats = env_stats.entry(environment.to_string()).or_insert_with(|| {
            EnvironmentStats {
                environment_name: environment.to_string(),
                server_address: String::new(), // 需要从配置中获取
                total_connections: 0,
                active_connections: 0,
                total_bytes_sent: 0,
                total_bytes_received: 0,
                connection_errors: 0,
                last_connection_time: None,
                port_mappings: Vec::new(),
            }
        });

        stats.total_connections += 1;
        stats.active_connections += 1;
        stats.last_connection_time = Some(Instant::now());

        // 更新端口映射统计
        if let Some(port_stats) = stats.port_mappings.iter_mut()
            .find(|p| p.local_port == local_port && p.remote_port == remote_port) {
            port_stats.connections += 1;
            port_stats.last_activity = Some(Instant::now());
        } else {
            stats.port_mappings.push(PortMappingStats {
                local_port,
                remote_port,
                connections: 1,
                bytes_sent: 0,
                bytes_received: 0,
                last_activity: Some(Instant::now()),
            });
        }

        debug!("Recorded new connection for environment: {}, port: {}->{}",
               environment, local_port, remote_port);
    }

    /// 记录连接断开
    pub fn record_disconnection(&self, session_id: u32) {
        self.active_connections.fetch_sub(1, Ordering::Relaxed);

        // 移除会话统计
        if let Some(session) = self.session_stats.write().unwrap().remove(&session_id) {
            // 更新环境统计
            let mut env_stats = self.environment_stats.write().unwrap();
            if let Some(stats) = env_stats.get_mut(&session.environment) {
                stats.active_connections = stats.active_connections.saturating_sub(1);
            }

            debug!("Recorded disconnection for session: {}", session_id);
        }
    }

    /// 记录数据传输
    pub fn record_data_transfer(&self, session_id: u32, bytes_sent: u64, bytes_received: u64) {
        self.total_bytes_sent.fetch_add(bytes_sent, Ordering::Relaxed);
        self.total_bytes_received.fetch_add(bytes_received, Ordering::Relaxed);

        // 更新会话统计
        if let Some(session) = self.session_stats.read().unwrap().get(&session_id) {
            session.bytes_sent.fetch_add(bytes_sent, Ordering::Relaxed);
            session.bytes_received.fetch_add(bytes_received, Ordering::Relaxed);
            *session.last_activity.write().unwrap() = Instant::now();

            // 更新环境和端口映射统计
            let mut env_stats = self.environment_stats.write().unwrap();
            if let Some(stats) = env_stats.get_mut(&session.environment) {
                stats.total_bytes_sent += bytes_sent;
                stats.total_bytes_received += bytes_received;

                if let Some(port_stats) = stats.port_mappings.iter_mut()
                    .find(|p| p.local_port == session.local_port && p.remote_port == session.remote_port) {
                    port_stats.bytes_sent += bytes_sent;
                    port_stats.bytes_received += bytes_received;
                    port_stats.last_activity = Some(Instant::now());
                }
            }
        }
    }

    /// 记录连接错误
    pub fn record_connection_error(&self, environment: &str) {
        self.connection_errors.fetch_add(1, Ordering::Relaxed);

        let mut env_stats = self.environment_stats.write().unwrap();
        if let Some(stats) = env_stats.get_mut(environment) {
            stats.connection_errors += 1;
        }
    }

    /// 记录超时错误
    pub fn record_timeout_error(&self) {
        self.timeout_errors.fetch_add(1, Ordering::Relaxed);
    }

    /// 创建会话统计
    pub fn create_session_stats(&self, session_id: u32, environment: &str, local_port: u16, remote_port: u16) {
        let session_stats = SessionStats {
            session_id,
            environment: environment.to_string(),
            local_port,
            remote_port,
            start_time: Instant::now(),
            bytes_sent: AtomicU64::new(0),
            bytes_received: AtomicU64::new(0),
            last_activity: Arc::new(RwLock::new(Instant::now())),
        };

        self.session_stats.write().unwrap().insert(session_id, session_stats);
    }

    /// 获取性能指标快照
    pub fn get_snapshot(&self) -> MetricsSnapshot {
        let uptime = self.start_time.elapsed();
        let total_bytes = self.total_bytes_sent.load(Ordering::Relaxed) +
                         self.total_bytes_received.load(Ordering::Relaxed);
        let throughput_bps = if uptime.as_secs() > 0 {
            total_bytes as f64 / uptime.as_secs() as f64
        } else {
            0.0
        };

        let environments: Vec<EnvironmentStats> = self.environment_stats
            .read()
            .unwrap()
            .values()
            .cloned()
            .collect();

        MetricsSnapshot {
            uptime_seconds: uptime.as_secs(),
            total_connections: self.total_connections.load(Ordering::Relaxed),
            active_connections: self.active_connections.load(Ordering::Relaxed),
            total_bytes_sent: self.total_bytes_sent.load(Ordering::Relaxed),
            total_bytes_received: self.total_bytes_received.load(Ordering::Relaxed),
            connection_errors: self.connection_errors.load(Ordering::Relaxed),
            timeout_errors: self.timeout_errors.load(Ordering::Relaxed),
            environments,
            active_sessions: self.session_stats.read().unwrap().len() as u64,
            throughput_bps,
        }
    }

    /// 启动定期统计报告
    pub async fn start_periodic_reporting(&self, interval_secs: u64) {
        let mut interval = interval(Duration::from_secs(interval_secs));

        loop {
            interval.tick().await;
            let snapshot = self.get_snapshot();

            info!("Proxy Forward Metrics Report:");
            info!("  Uptime: {}s", snapshot.uptime_seconds);
            info!("  Total Connections: {}", snapshot.total_connections);
            info!("  Active Connections: {}", snapshot.active_connections);
            info!("  Total Bytes Sent: {}", snapshot.total_bytes_sent);
            info!("  Total Bytes Received: {}", snapshot.total_bytes_received);
            info!("  Throughput: {:.2} bytes/sec", snapshot.throughput_bps);
            info!("  Connection Errors: {}", snapshot.connection_errors);
            info!("  Timeout Errors: {}", snapshot.timeout_errors);

            for env in &snapshot.environments {
                info!("  Environment '{}': {} active, {} total connections",
                      env.environment_name, env.active_connections, env.total_connections);
            }
        }
    }

    /// 清理过期的会话统计
    pub fn cleanup_expired_sessions(&self, max_age: Duration) {
        let now = Instant::now();
        let mut session_stats = self.session_stats.write().unwrap();

        session_stats.retain(|_, session| {
            let last_activity = *session.last_activity.read().unwrap();
            now.duration_since(last_activity) < max_age
        });
    }

    /// 重置所有统计信息
    pub fn reset(&self) {
        self.total_connections.store(0, Ordering::Relaxed);
        self.active_connections.store(0, Ordering::Relaxed);
        self.total_bytes_sent.store(0, Ordering::Relaxed);
        self.total_bytes_received.store(0, Ordering::Relaxed);
        self.connection_errors.store(0, Ordering::Relaxed);
        self.timeout_errors.store(0, Ordering::Relaxed);

        self.environment_stats.write().unwrap().clear();
        self.session_stats.write().unwrap().clear();

        info!("Proxy forward metrics reset");
    }
}

impl Default for ProxyForwardMetrics {
    fn default() -> Self {
        Self::new()
    }
}
