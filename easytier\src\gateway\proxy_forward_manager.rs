use std::sync::Arc;

use anyhow::Result;
use tracing::info;

use crate::{
    common::{config::ProxyForwardConfig, global_ctx::ArcGlobalCtx},
    peers::peer_manager::PeerManager,
};

use super::{proxy_forward_client::ProxyForwardClient, proxy_forward_server::ProxyForwardServer};

#[derive(Debug)]
pub struct ProxyForwardManager {
    global_ctx: ArcGlobalCtx,
    peer_manager: Arc<PeerManager>,
    client: Option<Arc<ProxyForwardClient>>,
    server: Arc<ProxyForwardServer>,
}

impl ProxyForwardManager {
    pub fn new(global_ctx: ArcGlobalCtx, peer_manager: Arc<PeerManager>) -> Self {
        let server = Arc::new(ProxyForwardServer::new(
            global_ctx.clone(),
            peer_manager.clone(),
        ));

        Self {
            global_ctx,
            peer_manager,
            client: None,
            server,
        }
    }

    pub async fn start(&mut self) -> Result<()> {
        info!("Starting proxy forward manager");

        // Start server (always available to handle incoming proxy requests)
        self.server.start().await?;

        // Start client if proxy forward config is available
        if let Some(proxy_config) = self.global_ctx.config.get_proxy_forwards() {
            let client = Arc::new(ProxyForwardClient::new(
                self.global_ctx.clone(),
                self.peer_manager.clone(),
                proxy_config,
            ));

            client.start().await?;
            self.client = Some(client);
            info!("Proxy forward client started");
        }

        // Register RPC service
        self.register_rpc_service().await;

        info!("Proxy forward manager started successfully");
        Ok(())
    }

    async fn register_rpc_service(&self) {
        // Register the proxy forward RPC service with the peer manager
        // This would be done through the RPC registry
        info!("Registering proxy forward RPC service");

        // In a real implementation, you would do something like:
        // self.peer_manager
        //     .get_peer_rpc_mgr()
        //     .rpc_server()
        //     .registry()
        //     .register(
        //         ProxyForwardRpcServer::new(self.server.clone()),
        //         &self.global_ctx.get_network_name(),
        //     );
    }

    pub fn get_client(&self) -> Option<Arc<ProxyForwardClient>> {
        self.client.clone()
    }

    pub fn get_server(&self) -> Arc<ProxyForwardServer> {
        self.server.clone()
    }

    pub async fn update_config(&mut self, new_config: Option<ProxyForwardConfig>) -> Result<()> {
        match new_config {
            Some(config) => {
                if self.client.is_none() {
                    // Create new client
                    let client = Arc::new(ProxyForwardClient::new(
                        self.global_ctx.clone(),
                        self.peer_manager.clone(),
                        config,
                    ));

                    client.start().await?;
                    self.client = Some(client);
                    info!("Proxy forward client started with new config");
                } else {
                    // For now, we don't support dynamic config updates
                    // In a real implementation, you would need to restart the client
                    info!("Proxy forward config update not yet supported - restart required");
                }
            }
            None => {
                if self.client.is_some() {
                    // Stop client
                    self.client = None;
                    info!("Proxy forward client stopped");
                }
            }
        }
        Ok(())
    }
}

impl Clone for ProxyForwardManager {
    fn clone(&self) -> Self {
        Self {
            global_ctx: self.global_ctx.clone(),
            peer_manager: self.peer_manager.clone(),
            client: self.client.clone(),
            server: self.server.clone(),
        }
    }
}
