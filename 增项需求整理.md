# P2P网络连接功能拓展需求详细方案

## 一、需求概述

实现两个组网设备间的P2P连接，使A设备能够通过P2P连接访问B设备所在局域网的服务器（如MySQL、SSH等服务）。开发人员可在A设备上预先配置对B设备的端口代理转发，直接对虚拟内网的B设备发起端口请求，由B设备进行反向代理将流量转发到目标设备上。

## 二、技术实现步骤

### 1. P2P连接建立机制
 
1. **NAT穿透技术选型**：
   - 利用现有的UDP打洞技术（如`udp_hole_punch`模块）
   - 根据NAT类型选择合适的穿透方法（`cone_to_cone`、`sym_to_cone`、`both_easy_sym`等）
   - 对于无法直接穿透的情况，使用中继服务器方案

2. **连接建立流程**：
   - 设备启动时初始化P2P连接模块
   - 通过STUN服务器获取公网IP和NAT类型
   - 使用PeerManager管理所有P2P连接
   - 建立直接连接（`try_direct_connect`）或通过打洞建立连接

### 2. 端口代理转发机制

1. **TCP代理模块扩展**：
   - 扩展现有的`tcp_proxy.rs`模块
   - 实现`NatDstConnector` trait的新连接器
   - 支持多种传输类型（TCP、KCP等）

2. **代理配置管理**：
   - 设计配置文件格式，支持多目标服务器配置
   - 实现配置的动态加载和更新机制
   - 提供命令行和API接口进行配置管理

## 三、A设备访问B设备局域网服务的具体流程

1. **网络发现阶段**：
   - A设备启动后连接到P2P网络
   - 通过路由表发现B设备（`list_peers`和`list_routes`）
   - 获取B设备的网络信息和可用服务列表

2. **连接建立阶段**：
   - A设备向B设备发起P2P连接请求
   - 双方进行NAT穿透（使用`UdpHolePunchRpc`服务）
   - 建立加密的P2P通道（`add_client_tunnel`）

3. **服务访问阶段**：
   - A设备向B设备发送端口转发请求
   - B设备验证请求合法性
   - B设备建立到目标服务器的连接
   - 双向数据流转发开始

## 四、开发人员在A设备上配置B设备的端口代理转发的操作步骤

1. **配置文件设置**：
   - 创建代理配置文件（toml格式）
   - 配置本地端口与远程服务的映射关系

```toml
[environments.server]
server = "*************"
port = [
   "400-22",
   "401-3306"
]

[environments.jumpserver]
server = "*************"
port = [
   "500-22"
]
```

- 配置文件解读,双方建立连接后,A设备可以通过访问B设备的ip:400端口访问*************的22端口,访问B设备的ip:401端口访问*************的3306端口,访问B设备的ip:500端口访问*************的22端口

- easytier-cli 可以通过命令生成样板配置文件,比如命令'easytier-cli init-config',将上面的配置写入到配置文件中

## 五、B设备反向代理实现方案

1. **代理服务模块设计**：
   - 实现`ProxyService`类处理代理请求
   - 集成到现有的`PeerManager`框架中
   - 支持TCP和UDP协议的转发

2. **连接管理机制**：
   - 使用连接池管理目标服务器连接
   - 实现超时和断线重连机制
   - 提供连接状态监控和统计

3. **数据转发实现**：
   - 使用高效的异步I/O处理数据转发
   - 实现数据缓冲和流控机制
   - 支持大文件传输和高并发连接

4. **安全机制**：
   - 实现访问控制列表（ACL）
   - 支持流量加密和身份验证
   - 提供审计日志记录功能

## 六、开发任务列表

1. **基础架构开发**
   - 扩展现有P2P连接模块，支持稳定的NAT穿透
   - 设计并实现代理配置管理系统
   - 开发命令行工具进行代理配置

2. **A设备端开发**
   - 实现本地端口监听和连接管理
   - 开发与B设备的代理协议通信模块
   - 实现连接状态监控和故障恢复机制

3. **B设备端开发**
   - 实现代理请求处理服务
   - 开发到目标服务器的连接管理
   - 实现双向数据流转发机制

4. **安全与性能优化**
   - 实现端到端加密和身份验证
   - 优化数据传输性能，减少延迟
   - 开发流量控制和限速机制

5. **测试与文档**
   - 编写单元测试和集成测试
   - 进行不同网络环境下的穿透测试
   - 编写用户文档和开发文档

## 七、业务流程图

1. **系统初始化流程**
   A设备启动 → 加载代理配置 → 连接P2P网络 → 发现B设备 → 建立P2P连接 → 就绪等待服务请求

2. **代理配置流程**
   开发人员输入命令 → 解析命令参数 → 验证配置有效性 → 保存代理配置 → 通知代理服务更新 → 返回配置结果

3. **服务访问流程**
   应用连接本地端口 → A设备接收连接 → 查找对应代理配置 → 通过P2P通道发送代理请求 → B设备接收请求 → B设备连接目标服务器 → 建立双向数据通道 → 数据转发开始 → 连接关闭时清理资源

4. **异常处理流程**
   检测连接异常 → 尝试重新建立P2P连接 → 连接恢复后重建代理 → 连接无法恢复时通知应用 → 记录错误日志

## 八、技术选型建议

1. **传输协议**：优先使用KCP协议，提供可靠的UDP传输
2. **加密方案**：采用TLS或自定义加密协议保证数据安全
3. **配置存储**：使用Toml格式存储配置，支持版本控制
4. **性能优化**：使用异步I/O和零拷贝技术提升转发性能

通过以上详细方案，可以实现开发人员在A设备上通过P2P网络访问B设备所在局域网服务的需求，提供便捷、安全的远程服务访问体验。
